# LawVriksh Backend Production Environment Configuration
# =============================================================================
# DOMAIN CONFIGURATION
# =============================================================================
DOMAIN=lawvriksh.com
API_BASE_URL=https://lawvriksh.com/api
FRONTEND_URL=https://lawvriksh.com

# =============================================================================
# DATABASE CONFIGURATION (MySQL)
# =============================================================================
# =============================================================================
# DATABASE CONFIGURATION (MySQL)
# =============================================================================
# Corrected to match the service name in docker-compose.yml
DB_HOST=db
DB_PORT=3306
DB_NAME=lawvriksh_referral

# Corrected to match the user created by docker-compose.yml
DB_USER=lawuser
DB_PASSWORD=lawpass123

# This is for the database container itself, not the application
MYSQL_ROOT_PASSWORD=rootpassword

# Corrected Database URL for SQLAlchemy
DATABASE_URL=mysql+pymysql://lawuser:lawpass123@db:3306/lawvriksh_referral


# =============================================================================
# REDIS CACHE CONFIGURATION
# =============================================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=Sahil@123
REDIS_URL=redis://:Sahil@123@redis:6379/0

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret Key (using existing key)
JWT_SECRET_KEY=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456

# Application Secret Key (using existing key)
SECRET_KEY=9876543210fedcba0987654321fedcba0987654321fedcba0987654321fedcba

# Password Salt
PASSWORD_SALT=abcdef1234567890fedcba0987654321

# =============================================================================
# EMAIL CONFIGURATION (Hostinger SMTP)
# =============================================================================
EMAIL_FROM=<EMAIL>
SMTP_HOST=smtp.hostinger.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=Lawvriksh@123

# Email Templates
WELCOME_EMAIL_TEMPLATE=welcome_template
FEEDBACK_EMAIL_TEMPLATE=feedback_template

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
CACHE_DIR=/app/cache

# Security Headers
ALLOWED_HOSTS=lawvriksh.com,www.lawvriksh.com
CORS_ORIGINS=https://lawvriksh.com,https://www.lawvriksh.com,http://localhost:3000

# Session Configuration
SESSION_TIMEOUT=3600
SESSION_SECURE=true
SESSION_HTTPONLY=true

# =============================================================================
# CELERY CONFIGURATION (Background Tasks)
# =============================================================================
CELERY_BROKER_URL=redis://:Sahil@123@redis:6379/1
CELERY_RESULT_BACKEND=redis://:Sahil@123@redis:6379/2

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE=16777216
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx
UPLOAD_DIR=/app/uploads

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_FORMAT=json
LOG_ROTATION=daily
LOG_RETENTION_DAYS=30

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
# Database Connection Pool
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Cache Settings
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Worker Settings
WORKER_PROCESSES=4
WORKER_CONNECTIONS=1000
WORKER_TIMEOUT=120

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_SOCIAL_LOGIN=false
ENABLE_TWO_FACTOR_AUTH=false

# =============================================================================
# DEVELOPMENT OVERRIDES (Keep false in production)
# =============================================================================
ENABLE_CORS=true
ENABLE_DEBUG_TOOLBAR=false
ENABLE_PROFILING=false

